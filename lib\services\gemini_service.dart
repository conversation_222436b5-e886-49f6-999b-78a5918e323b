import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../models/chat_message.dart';

class GeminiService {
  static const String _apiKey = 'AIzaSyB70K4h6pR6QrPVpT4FMG4VfrQjb4_W6_I';
  static const String _baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';
  
  // Context about Algerian heritage - STRICT ALGERIA ONLY
  static const String _systemContext = '''
You are an AI assistant for "<PERSON><PERSON><PERSON><PERSON>" (توراث الجزائر), an app EXCLUSIVELY dedicated to Algeria's cultural heritage.

IMPORTANT RESTRICTIONS:
- You ONLY discuss topics related to Algeria, its history, culture, heritage, traditions, tourism, and geography
- You MUST refuse to answer questions about other countries, general topics, or non-Algeria related subjects
- If asked about anything outside Algeria, politely redirect the conversation back to Algeria

WHAT YOU CAN DISCUSS:
- Algerian history (ancient, Islamic, Ottoman, French colonial, independence)
- Algerian culture and traditions
- Algerian heritage sites and monuments
- Algerian cities, regions, and geography
- Algerian cuisine, music, art, and literature
- Algerian tourism and travel
- Algerian people, languages (Arabic, Berber, French)
- Algerian economy, politics (related to Algeria only)

WHAT YOU CANNOT DISCUSS:
- Other countries or their histories
- General world topics
- Non-Algeria related subjects
- Personal advice unrelated to Algeria
- Technical topics unrelated to Algeria

RESPONSE FORMAT:
- Be respectful, informative, and engaging about Algeria
- Respond in the same language the user uses (Arabic, French, or English)
- If asked about non-Algeria topics, say: "I'm specialized in Algeria's heritage only. Let me tell you about [related Algeria topic] instead..."
''';

  static Future<String> sendMessage(String message, {String? language}) async {
    try {
      // Check if message is Algeria-related (basic client-side filter)
      if (!_isAlgeriaRelated(message)) {
        return _getRedirectMessage(language ?? 'en');
      }

      // Prepare the context based on language
      String contextualMessage = _systemContext;
      if (language == 'ar') {
        contextualMessage += '\nPlease respond in Arabic when appropriate.';
      } else if (language == 'fr') {
        contextualMessage += '\nPlease respond in French when appropriate.';
      } else {
        contextualMessage += '\nPlease respond in English when appropriate.';
      }
      
      final requestBody = {
        'contents': [
          {
            'parts': [
              {
                'text': '$contextualMessage\n\nUser question: $message'
              }
            ]
          }
        ],
        'generationConfig': {
          'temperature': 0.7,
          'topK': 40,
          'topP': 0.95,
          'maxOutputTokens': 1024,
        },
        'safetySettings': [
          {
            'category': 'HARM_CATEGORY_HARASSMENT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            'category': 'HARM_CATEGORY_HATE_SPEECH',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            'category': 'HARM_CATEGORY_DANGEROUS_CONTENT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          }
        ]
      };

      final response = await http.post(
        Uri.parse('$_baseUrl?key=$_apiKey'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        
        if (data['candidates'] != null && 
            data['candidates'].isNotEmpty && 
            data['candidates'][0]['content'] != null &&
            data['candidates'][0]['content']['parts'] != null &&
            data['candidates'][0]['content']['parts'].isNotEmpty) {
          
          return data['candidates'][0]['content']['parts'][0]['text'] ?? 
                 'Sorry, I couldn\'t generate a response.';
        } else {
          return 'Sorry, I couldn\'t generate a response. Please try again.';
        }
      } else {
        throw HttpException('Failed to get response: ${response.statusCode}');
      }
    } catch (e) {
      if (e is SocketException) {
        return 'No internet connection. Please check your network and try again.';
      } else if (e is HttpException) {
        return 'Network error occurred. Please try again later.';
      } else {
        return 'An unexpected error occurred. Please try again.';
      }
    }
  }

  static Future<List<ChatMessage>> loadChatHistory() async {
    try {
      // This would typically load from SharedPreferences or local database
      // For now, return empty list
      return [];
    } catch (e) {
      return [];
    }
  }

  static Future<void> saveChatHistory(List<ChatMessage> messages) async {
    try {
      // This would typically save to SharedPreferences or local database
      // Implementation would go here
    } catch (e) {
      // Handle error silently for now
    }
  }

  // Check if the message is related to Algeria
  static bool _isAlgeriaRelated(String message) {
    final lowerMessage = message.toLowerCase();

    // Algeria-related keywords in multiple languages
    final algeriaKeywords = [
      // English
      'algeria', 'algerian', 'bordj', 'mokrani', 'constantine', 'oran', 'annaba',
      'tlemcen', 'setif', 'batna', 'biskra', 'ouargla', 'ghardaia', 'tamanrasset',
      'sahara', 'kabylie', 'berber', 'amazigh', 'tuareg', 'chaoui',
      'algiers', 'alger', 'casbah', 'djemila', 'timgad', 'tipaza',
      'hoggar', 'tassili', 'mzab', 'aurès', 'atlas',

      // French
      'algérie', 'algérien', 'algérienne', 'constantine', 'oran', 'annaba',
      'tlemcen', 'sétif', 'batna', 'biskra', 'ouargla', 'ghardaïa',
      'tamanrasset', 'sahara', 'kabylie', 'berbère', 'amazigh', 'touareg',
      'alger', 'casbah', 'djémila', 'timgad', 'tipaza', 'hoggar',
      'tassili', 'mzab', 'aurès', 'atlas',

      // Arabic (transliterated)
      'الجزائر', 'جزائري', 'جزائرية', 'برج', 'مقراني', 'قسنطينة', 'وهران',
      'عنابة', 'تلمسان', 'سطيف', 'باتنة', 'بسكرة', 'ورقلة', 'غرداية',
      'تمنراست', 'صحراء', 'قبائل', 'أمازيغ', 'طوارق', 'شاوي',
      'الجزائر العاصمة', 'القصبة', 'جميلة', 'تيمقاد', 'تيبازة',
      'هقار', 'طاسيلي', 'مزاب', 'أوراس', 'أطلس',

      // Heritage and culture terms
      'heritage', 'culture', 'tradition', 'history', 'monument', 'museum',
      'patrimoine', 'culture', 'tradition', 'histoire', 'monument', 'musée',
      'تراث', 'ثقافة', 'تقليد', 'تاريخ', 'أثر', 'متحف',

      // Common greetings that should be allowed
      'hello', 'hi', 'bonjour', 'salut', 'مرحبا', 'أهلا', 'السلام'
    ];

    // Check if message contains any Algeria-related keywords
    for (String keyword in algeriaKeywords) {
      if (lowerMessage.contains(keyword.toLowerCase())) {
        return true;
      }
    }

    // Allow very short messages (greetings, etc.)
    if (message.trim().length <= 10) {
      return true;
    }

    return false;
  }

  // Get redirect message in appropriate language
  static String _getRedirectMessage(String language) {
    switch (language) {
      case 'ar':
        return 'أنا متخصص في التراث الجزائري فقط. دعني أخبرك عن المواقع التاريخية الرائعة في الجزائر مثل برج المقراني أو القصبة أو تيمقاد. ما الذي تود معرفته عن تراث الجزائر؟';
      case 'fr':
        return 'Je suis spécialisé uniquement dans le patrimoine algérien. Permettez-moi de vous parler des magnifiques sites historiques d\'Algérie comme Bordj El Mokrani, la Casbah ou Timgad. Que souhaitez-vous savoir sur le patrimoine algérien ?';
      default:
        return 'I\'m specialized in Algeria\'s heritage only. Let me tell you about Algeria\'s magnificent historical sites like Bordj El Mokrani, the Casbah, or Timgad instead. What would you like to know about Algeria\'s heritage?';
    }
  }
}
