import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../models/chat_message.dart';

class GeminiService {
  static const String _apiKey = 'AIzaSyB70K4h6pR6QrPVpT4FMG4VfrQjb4_W6_I';
  static const String _baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';
  
  // Context about Algerian heritage to make responses more relevant
  static const String _systemContext = '''
You are an AI assistant for "Turat<PERSON><PERSON>" (توراث الجزائر), an app dedicated to Algeria's cultural heritage. 
You specialize in Algerian history, culture, traditions, and heritage sites. You should provide helpful, 
accurate information about Algeria's rich cultural heritage, historical sites, traditions, and customs.
When users ask about other topics, you can help but try to relate back to Algerian culture when appropriate.
Be respectful, informative, and engaging. Respond in the same language the user uses (Arabic, French, or English).
''';

  static Future<String> sendMessage(String message, {String? language}) async {
    try {
      // Prepare the context based on language
      String contextualMessage = _systemContext;
      if (language == 'ar') {
        contextualMessage += '\nPlease respond in Arabic when appropriate.';
      } else if (language == 'fr') {
        contextualMessage += '\nPlease respond in French when appropriate.';
      } else {
        contextualMessage += '\nPlease respond in English when appropriate.';
      }
      
      final requestBody = {
        'contents': [
          {
            'parts': [
              {
                'text': '$contextualMessage\n\nUser question: $message'
              }
            ]
          }
        ],
        'generationConfig': {
          'temperature': 0.7,
          'topK': 40,
          'topP': 0.95,
          'maxOutputTokens': 1024,
        },
        'safetySettings': [
          {
            'category': 'HARM_CATEGORY_HARASSMENT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            'category': 'HARM_CATEGORY_HATE_SPEECH',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          },
          {
            'category': 'HARM_CATEGORY_DANGEROUS_CONTENT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE'
          }
        ]
      };

      final response = await http.post(
        Uri.parse('$_baseUrl?key=$_apiKey'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        
        if (data['candidates'] != null && 
            data['candidates'].isNotEmpty && 
            data['candidates'][0]['content'] != null &&
            data['candidates'][0]['content']['parts'] != null &&
            data['candidates'][0]['content']['parts'].isNotEmpty) {
          
          return data['candidates'][0]['content']['parts'][0]['text'] ?? 
                 'Sorry, I couldn\'t generate a response.';
        } else {
          return 'Sorry, I couldn\'t generate a response. Please try again.';
        }
      } else {
        throw HttpException('Failed to get response: ${response.statusCode}');
      }
    } catch (e) {
      if (e is SocketException) {
        return 'No internet connection. Please check your network and try again.';
      } else if (e is HttpException) {
        return 'Network error occurred. Please try again later.';
      } else {
        return 'An unexpected error occurred. Please try again.';
      }
    }
  }

  static Future<List<ChatMessage>> loadChatHistory() async {
    try {
      // This would typically load from SharedPreferences or local database
      // For now, return empty list
      return [];
    } catch (e) {
      return [];
    }
  }

  static Future<void> saveChatHistory(List<ChatMessage> messages) async {
    try {
      // This would typically save to SharedPreferences or local database
      // Implementation would go here
    } catch (e) {
      // Handle error silently for now
    }
  }
}
